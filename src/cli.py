import argparse, json
from .serial_link import SerialLink

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--port', default=None, help='Serial port (overrides config.json)')
    ap.add_argument('--baud', type=int, default=None, help='Baud rate')
    ap.add_argument('--pwm', type=int, default=150)
    ap.add_argument('--mode', type=int, default=0)
    ap.add_argument('--flags', type=int, default=0)
    ap.add_argument('--config', default='src/config.json')
    args = ap.parse_args()

    cfg = json.load(open(args.config))
    port = args.port or cfg['port']
    baud = args.baud or cfg['baud']

    # Auto-select port if "auto"
    if str(port).lower() == "auto":
        try:
            from .serial_utils import auto_select_port
            auto = auto_select_port()
            if auto:
                print(f"Auto-selected serial port: {auto}")
                port = auto
            else:
                raise RuntimeError("No suitable serial port found.")
        except Exception as e:
            raise

    link = SerialLink(port, baud)
    link.open()
    link.send_command(args.pwm, args.mode, args.flags)
    print(f"Sent command: pwm={args.pwm} mode={args.mode} flags={args.flags}")
    link.close()

if __name__ == '__main__':
    main()
