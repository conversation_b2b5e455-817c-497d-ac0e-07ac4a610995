import sys
from typing import List, Optional, Tuple

try:
    from serial.tools import list_ports
except Exception:  # pyserial not installed or other import error
    list_ports = None  # type: ignore


class PortInfo:
    def __init__(self, device: str, description: str = "", manufacturer: str = "",
                 vid: Optional[int] = None, pid: Optional[int] = None, hwid: str = ""):
        self.device = device
        self.description = description or ""
        self.manufacturer = manufacturer or ""
        self.vid = vid
        self.pid = pid
        self.hwid = hwid or ""

    @classmethod
    def from_comport(cls, p):
        return cls(
            device=p.device,
            description=getattr(p, "description", "") or "",
            manufacturer=getattr(p, "manufacturer", "") or "",
            vid=getattr(p, "vid", None),
            pid=getattr(p, "pid", None),
            hwid=getattr(p, "hwid", "") or "",
        )

    def __repr__(self) -> str:
        vidpid = (
            f"VID:PID={self.vid:04X}:{self.pid:04X}" if self.vid is not None and self.pid is not None else "VID:PID=?"
        )
        return f"{self.device}  {self.description}  {self.manufacturer}  {vidpid}  {self.hwid}"


def _get_all_ports() -> List[PortInfo]:
    if list_ports is None:
        return []
    try:
        return [PortInfo.from_comport(p) for p in list_ports.comports()]
    except Exception:
        return []


def _score_port(p: PortInfo) -> int:
    # Heuristics for macOS ESP32 dev boards using CH340 or CP210x
    desc = (p.description or "").lower()
    manu = (p.manufacturer or "").lower()
    hwid = (p.hwid or "").lower()
    dev = (p.device or "").lower()

    score = 0
    # Common macOS device name patterns
    if dev.startswith("/dev/cu."):
        score += 1
    if "usbserial" in dev or "wchusbserial" in dev or "slab_usbto" in dev:
        score += 2

    # Vendor IDs
    if p.vid == 0x1A86:  # QinHeng/WCH (CH340)
        score += 5
    if p.vid == 0x10C4:  # Silicon Labs (CP210x)
        score += 4

    # Textual hints
    for token in ["ch340", "wch", "cp210", "silicon labs", "esp32", "usb-serial"]:
        if token in desc or token in manu or token in hwid:
            score += 2

    return score


def find_candidate_esp32_ports() -> List[Tuple[PortInfo, int]]:
    ports = _get_all_ports()
    ranked = [(p, _score_port(p)) for p in ports]
    ranked.sort(key=lambda t: t[1], reverse=True)
    return ranked


def auto_select_port(min_score: int = 1) -> Optional[str]:
    ranked = find_candidate_esp32_ports()
    for p, score in ranked:
        if score >= min_score:
            return p.device
    return None


def format_port_line(p: PortInfo, score: int) -> str:
    vidpid = (
        f"{p.vid:04X}:{p.pid:04X}" if p.vid is not None and p.pid is not None else "????:????"
    )
    return f"{p.device:24}  score={score:2d}  {vidpid}  {p.description}  {p.manufacturer}"


def is_pyserial_available() -> bool:
    return list_ports is not None


if __name__ == "__main__":
    if not is_pyserial_available():
        print("pyserial not installed. Install with: pip install pyserial", file=sys.stderr)
        sys.exit(1)
    ranked = find_candidate_esp32_ports()
    if not ranked:
        print("No serial ports found.")
        sys.exit(1)
    for p, s in ranked:
        print(format_port_line(p, s))
    auto = auto_select_port()
    print(f"Suggested port: {auto if auto else 'None'}")

