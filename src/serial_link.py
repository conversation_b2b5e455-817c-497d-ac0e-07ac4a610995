import serial, threading, queue, time

class SerialLink:
    def __init__(self, port:str, baud:int=2_000_000, timeout:float=0.05):
        self.port = port
        self.baud = baud
        self.timeout = timeout
        self._ser = None
        self._rx = queue.Queue(maxsize=1000)
        self._running = False
        self._rx_thread = None

    def open(self):
        self._ser = serial.Serial(self.port, self.baud, timeout=self.timeout, rtscts=False, dsrdtr=False, write_timeout=0)
        self._running = True
        self._rx_thread = threading.Thread(target=self._reader, daemon=True)
        self._rx_thread.start()

    def close(self):
        self._running = False
        if self._ser:
            try:
                self._ser.close()
            except Exception:
                pass
            self._ser = None

    def _reader(self):
        buf = b""
        while self._running and self._ser:
            try:
                chunk = self._ser.read(4096)
                if not chunk:
                    continue
                buf += chunk
                while b"\n" in buf:
                    line, buf = buf.split(b"\n", 1)
                    self._rx.put(line.decode(errors='ignore').strip())
            except Exception as e:
                # Avoid tight loop on error
                time.sleep(0.01)

    def send_command(self, pwm:int, mode:int=0, flags:int=0):
        # Format: C,pwm,mode,flags\n (matches gateway sketch)
        line = f"C,{int(pwm)},{int(mode)},{int(flags)}\n".encode()
        try:
            self._ser.write(line)
        except Exception:
            pass

    def poll_line(self, timeout:float=0.0):
        try:
            return self._rx.get(timeout=timeout)
        except queue.Empty:
            return None
