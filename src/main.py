import json
import time
import tkinter as tk
from tkinter import ttk, scrolledtext
import threading
from datetime import datetime
from .serial_link import SerialLink
from .serial_utils import auto_select_port


class ESP32ConnectionApp:
    def __init__(self, root):
        self.root = root
        self.root.title("ESP32 PC Module Connection Status")
        self.root.geometry("600x500")

        # Connection state
        self.serial_link = None
        self.is_connected = False
        self.connection_thread = None
        self.monitoring_thread = None
        self.should_monitor = False

        # Load configuration
        try:
            with open('src/config.json', 'r') as f:
                self.config = json.load(f)
        except Exception as e:
            self.config = {
                "port": "auto",
                "baud": 2000000
            }

        self.setup_ui()
        self.start_connection_monitoring()

    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Title
        title_label = ttk.Label(main_frame, text="ESP32 PC Module Connection",
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Connection status
        ttk.Label(main_frame, text="Connection Status:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.status_label = ttk.Label(main_frame, text="Disconnected",
                                     foreground="red", font=("Arial", 12, "bold"))
        self.status_label.grid(row=1, column=1, sticky=tk.W, pady=5)

        # Port information
        ttk.Label(main_frame, text="Serial Port:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.port_label = ttk.Label(main_frame, text="Not detected")
        self.port_label.grid(row=2, column=1, sticky=tk.W, pady=5)

        # Baud rate
        ttk.Label(main_frame, text="Baud Rate:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.baud_label = ttk.Label(main_frame, text=str(self.config.get('baud', 2000000)))
        self.baud_label.grid(row=3, column=1, sticky=tk.W, pady=5)

        # Last message time
        ttk.Label(main_frame, text="Last Message:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.last_msg_label = ttk.Label(main_frame, text="Never")
        self.last_msg_label.grid(row=4, column=1, sticky=tk.W, pady=5)

        # Control buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=20)

        self.connect_btn = ttk.Button(button_frame, text="Connect", command=self.toggle_connection)
        self.connect_btn.pack(side=tk.LEFT, padx=5)

        self.refresh_btn = ttk.Button(button_frame, text="Refresh Ports", command=self.refresh_ports)
        self.refresh_btn.pack(side=tk.LEFT, padx=5)

        # Message log
        ttk.Label(main_frame, text="Message Log:").grid(row=6, column=0, sticky=tk.W, pady=(20, 5))

        self.log_text = scrolledtext.ScrolledText(main_frame, height=15, width=70)
        self.log_text.grid(row=7, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        main_frame.rowconfigure(7, weight=1)

        # Clear log button
        ttk.Button(main_frame, text="Clear Log", command=self.clear_log).grid(row=8, column=0, columnspan=2, pady=5)

    def log_message(self, message):
        """Add a message to the log with timestamp"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        # Limit log size to prevent memory issues
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 1000:
            self.log_text.delete("1.0", "100.0")

    def clear_log(self):
        """Clear the message log"""
        self.log_text.delete("1.0", tk.END)

    def refresh_ports(self):
        """Refresh available serial ports"""
        try:
            port = auto_select_port()
            if port:
                self.port_label.config(text=port)
                self.log_message(f"Found ESP32 port: {port}")
            else:
                self.port_label.config(text="Not detected")
                self.log_message("No ESP32 port detected")
        except Exception as e:
            self.log_message(f"Error refreshing ports: {e}")

    def toggle_connection(self):
        """Toggle connection to ESP32"""
        if self.is_connected:
            self.disconnect()
        else:
            self.connect()

    def connect(self):
        """Connect to ESP32"""
        try:
            # Get port
            port = self.config.get('port', 'auto')
            if str(port).lower() == "auto":
                port = auto_select_port()
                if not port:
                    self.log_message("ERROR: No ESP32 port found. Please connect ESP32 and try again.")
                    return

            # Create serial connection
            baud = self.config.get('baud', 2000000)
            self.serial_link = SerialLink(port, baud)
            self.serial_link.open()

            # Update UI
            self.is_connected = True
            self.status_label.config(text="Connected", foreground="green")
            self.port_label.config(text=port)
            self.connect_btn.config(text="Disconnect")

            self.log_message(f"Connected to ESP32 on {port} @ {baud} baud")

            # Start monitoring thread
            self.should_monitor = True
            self.monitoring_thread = threading.Thread(target=self.monitor_connection, daemon=True)
            self.monitoring_thread.start()

        except Exception as e:
            self.log_message(f"ERROR: Failed to connect: {e}")
            self.disconnect()

    def disconnect(self):
        """Disconnect from ESP32"""
        self.should_monitor = False

        if self.serial_link:
            try:
                self.serial_link.close()
            except Exception as e:
                self.log_message(f"Error closing connection: {e}")
            self.serial_link = None

        # Update UI
        self.is_connected = False
        self.status_label.config(text="Disconnected", foreground="red")
        self.connect_btn.config(text="Connect")
        self.last_msg_label.config(text="Never")

        self.log_message("Disconnected from ESP32")

    def monitor_connection(self):
        """Monitor the serial connection for incoming messages"""
        last_message_time = None

        while self.should_monitor and self.serial_link:
            try:
                # Poll for incoming data
                line = self.serial_link.poll_line(timeout=0.1)
                if line:
                    last_message_time = datetime.now()
                    self.root.after(0, lambda: self.last_msg_label.config(
                        text=last_message_time.strftime("%H:%M:%S")))
                    self.root.after(0, lambda: self.log_message(f"RX: {line}"))

                # Send periodic keep-alive
                if last_message_time is None or (datetime.now() - last_message_time).seconds > 2:
                    try:
                        # Send a simple keep-alive command
                        self.serial_link.send_command(0, 0, 0)
                    except Exception:
                        pass

            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"Monitor error: {e}"))
                break

        # If we exit the loop due to error, disconnect
        if self.should_monitor:
            self.root.after(0, self.disconnect)

    def start_connection_monitoring(self):
        """Start monitoring for available connections"""
        self.refresh_ports()

    def on_closing(self):
        """Handle application closing"""
        self.disconnect()
        self.root.destroy()


def main():
    root = tk.Tk()
    app = ESP32ConnectionApp(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()


if __name__ == '__main__':
    main()
