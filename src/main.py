import json, time
from pathlib import Path
from .serial_link import Serial<PERSON>ink
from .parser import parse_telemetry_line
from .logger import CsvLogger
from .policy import DummyPolicy

def main():
    cfg = json.load(open('src/config.json'))
    port = cfg['port']
    baud = cfg['baud']
    csv_path = cfg['csv_path']
    print_every = int(cfg.get('print_every', 1))
    cmd_rate_hz = float(cfg.get('command_rate_hz', 20))
    cmd_period = 1.0 / max(1.0, cmd_rate_hz)

    # Auto-select port on macOS if configured as "auto"
    if str(port).lower() == "auto":
        try:
            from .serial_utils import auto_select_port
            auto = auto_select_port()
            if auto:
                print(f"Auto-selected serial port: {auto}")
                port = auto
            else:
                raise RuntimeError("No suitable serial port found. Plug in ESP32 and try again.")
        except Exception as e:
            raise

    link = SerialLink(port, baud)
    link.open()
    logger = CsvLogger(csv_path)
    policy = DummyPolicy()

    print(f"Listening on {port} @ {baud} baud. Logging to {csv_path}")
    last_cmd = 0.0
    n = 0
    last_state = None

    try:
        while True:
            line = link.poll_line(timeout=0.05)
            if not line:
                # Periodically send a keep-alive command even if no telemetry arrives
                now = time.time()
                if now - last_cmd > cmd_period:
                    pwm, mode, flags = policy.act(last_state or {})
                    link.send_command(pwm, mode, flags)
                    last_cmd = now
                continue

            state = parse_telemetry_line(line)
            if state:
                last_state = state
                logger.write(state)
                n += 1
                if (n % max(1, print_every)) == 0:
                    print(state)

                # Decide & send command at fixed rate
                now = time.time()
                if now - last_cmd > cmd_period:
                    pwm, mode, flags = policy.act(state)
                    link.send_command(pwm, mode, flags)
                    last_cmd = now
    except KeyboardInterrupt:
        pass
    finally:
        logger.close()
        link.close()

if __name__ == '__main__':
    main()
