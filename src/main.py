import json
import time
import threading
from datetime import datetime
from .serial_link import SerialLink
from .serial_utils import auto_select_port


class ESP32ConnectionMonitor:
    def __init__(self):
        # Connection state
        self.serial_link = None
        self.is_connected = False
        self.should_monitor = False
        self.last_message_time = None

        # Load configuration
        try:
            with open('src/config.json', 'r') as f:
                self.config = json.load(f)
        except Exception as e:
            self.config = {
                "port": "auto",
                "baud": 2000000
            }

        print("=" * 60)
        print("ESP32 PC Module Connection Monitor")
        print("=" * 60)

    def log_message(self, message):
        """Print a message with timestamp"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")

    def find_esp32_port(self):
        """Find and return ESP32 port"""
        try:
            port = auto_select_port()
            if port:
                self.log_message(f"Found ESP32 port: {port}")
                return port
            else:
                self.log_message("No ESP32 port detected")
                return None
        except Exception as e:
            self.log_message(f"Error finding ports: {e}")
            return None

    def connect(self):
        """Connect to ESP32"""
        try:
            # Get port
            port = self.config.get('port', 'auto')
            if str(port).lower() == "auto":
                port = self.find_esp32_port()
                if not port:
                    self.log_message("ERROR: No ESP32 port found. Please connect ESP32 and try again.")
                    return False

            # Create serial connection
            baud = self.config.get('baud', 2000000)
            self.serial_link = SerialLink(port, baud)
            self.serial_link.open()

            # Update state
            self.is_connected = True
            self.log_message(f"✓ Connected to ESP32 on {port} @ {baud} baud")

            return True

        except Exception as e:
            self.log_message(f"✗ Failed to connect: {e}")
            self.disconnect()
            return False

    def disconnect(self):
        """Disconnect from ESP32"""
        self.should_monitor = False

        if self.serial_link:
            try:
                self.serial_link.close()
            except Exception as e:
                self.log_message(f"Error closing connection: {e}")
            self.serial_link = None

        self.is_connected = False
        self.last_message_time = None
        self.log_message("✗ Disconnected from ESP32")

    def monitor_connection(self):
        """Monitor the serial connection for incoming messages"""
        self.log_message("Starting connection monitoring...")
        message_count = 0

        while self.should_monitor and self.serial_link:
            try:
                # Poll for incoming data
                line = self.serial_link.poll_line(timeout=0.1)
                if line:
                    self.last_message_time = datetime.now()
                    message_count += 1
                    self.log_message(f"RX #{message_count}: {line}")

                # Send periodic keep-alive every 2 seconds
                if (self.last_message_time is None or
                    (datetime.now() - self.last_message_time).seconds > 2):
                    try:
                        # Send a simple keep-alive command
                        self.serial_link.send_command(0, 0, 0)
                        # Don't log every keep-alive to avoid spam
                    except Exception:
                        pass

            except Exception as e:
                self.log_message(f"Monitor error: {e}")
                break

        self.log_message("Connection monitoring stopped")

    def print_status(self):
        """Print current connection status"""
        print("\n" + "=" * 40)
        print("CONNECTION STATUS")
        print("=" * 40)
        print(f"Status: {'✓ Connected' if self.is_connected else '✗ Disconnected'}")

        if self.is_connected and self.serial_link:
            print(f"Port: {self.serial_link.port}")
            print(f"Baud Rate: {self.serial_link.baud}")
            if self.last_message_time:
                print(f"Last Message: {self.last_message_time.strftime('%H:%M:%S')}")
            else:
                print("Last Message: Never")
        else:
            available_port = self.find_esp32_port()
            print(f"Available Port: {available_port if available_port else 'None detected'}")
            print(f"Configured Baud: {self.config.get('baud', 2000000)}")
        print("=" * 40)

    def run_interactive(self):
        """Run interactive console interface"""
        self.print_status()

        while True:
            print("\nCommands:")
            print("  c - Connect to ESP32")
            print("  d - Disconnect")
            print("  s - Show status")
            print("  r - Refresh ports")
            print("  q - Quit")

            try:
                choice = input("\nEnter command: ").lower().strip()

                if choice == 'c':
                    if not self.is_connected:
                        if self.connect():
                            self.should_monitor = True
                            monitor_thread = threading.Thread(target=self.monitor_connection, daemon=True)
                            monitor_thread.start()
                    else:
                        self.log_message("Already connected!")

                elif choice == 'd':
                    if self.is_connected:
                        self.disconnect()
                    else:
                        self.log_message("Not connected!")

                elif choice == 's':
                    self.print_status()

                elif choice == 'r':
                    self.find_esp32_port()

                elif choice == 'q':
                    if self.is_connected:
                        self.disconnect()
                    print("Goodbye!")
                    break

                else:
                    print("Invalid command!")

            except KeyboardInterrupt:
                print("\n\nShutting down...")
                if self.is_connected:
                    self.disconnect()
                break
            except EOFError:
                print("\n\nShutting down...")
                if self.is_connected:
                    self.disconnect()
                break


def main():
    monitor = ESP32ConnectionMonitor()
    monitor.run_interactive()


if __name__ == '__main__':
    main()
