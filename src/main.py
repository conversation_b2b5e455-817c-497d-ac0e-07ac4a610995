import json
import time
import threading
from datetime import datetime
import sys
import os

# Add the parent directory to the path so we can import from src
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import (QA<PERSON>lication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QLabel, QPushButton, QTextEdit, QFrame, QGridLayout)
from PyQt5.QtCore import QTimer, pyqtSignal, QObject, QThread
from PyQt5.QtGui import QFont, QPalette

from src.serial_link import SerialLink
from src.serial_utils import auto_select_port


class ConnectionWorker(QObject):
    # Signals for thread communication
    message_received = pyqtSignal(str)
    connection_status_changed = pyqtSignal(bool, str)

    def __init__(self):
        super().__init__()
        self.serial_link = None
        self.should_monitor = False

    def connect_to_esp32(self, port, baud):
        """Connect to ESP32 in worker thread"""
        try:
            self.serial_link = SerialLink(port, baud)
            self.serial_link.open()
            self.connection_status_changed.emit(True, f"Connected to {port}")

            # Start monitoring
            self.should_monitor = True
            while self.should_monitor and self.serial_link:
                line = self.serial_link.poll_line(timeout=0.1)
                if line:
                    self.message_received.emit(line)

                # Send keep-alive every 2 seconds
                try:
                    self.serial_link.send_command(0, 0, 0)
                    time.sleep(0.1)
                except Exception:
                    pass

        except Exception as e:
            self.connection_status_changed.emit(False, f"Connection failed: {e}")

    def disconnect_from_esp32(self):
        """Disconnect from ESP32"""
        self.should_monitor = False
        if self.serial_link:
            try:
                self.serial_link.close()
            except Exception:
                pass
            self.serial_link = None
        self.connection_status_changed.emit(False, "Disconnected")


class ESP32ConnectionApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("ESP32 PC Module Connection Monitor")
        self.setGeometry(100, 100, 800, 600)

        # Load configuration
        try:
            with open('src/config.json', 'r') as f:
                self.config = json.load(f)
        except Exception:
            self.config = {"port": "auto", "baud": 2000000}

        # Connection state
        self.is_connected = False
        self.current_port = None
        self.message_count = 0

        # Worker thread
        self.worker_thread = None
        self.worker = None

        self.setup_ui()
        self.refresh_ports()

        # Timer for UI updates
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_ui)
        self.timer.start(1000)  # Update every second

    def setup_ui(self):
        """Setup the user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(15)

        # Title
        title = QLabel("ESP32 PC Module Connection Monitor")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin: 10px;")
        layout.addWidget(title)

        # Status frame
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.Box)
        status_frame.setStyleSheet("QFrame { border: 2px solid #bdc3c7; border-radius: 5px; padding: 10px; }")
        status_layout = QGridLayout(status_frame)

        # Connection status
        status_layout.addWidget(QLabel("Connection Status:"), 0, 0)
        self.status_label = QLabel("Disconnected")
        self.status_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.status_label.setStyleSheet("color: #e74c3c;")
        status_layout.addWidget(self.status_label, 0, 1)

        # Port info
        status_layout.addWidget(QLabel("Serial Port:"), 1, 0)
        self.port_label = QLabel("Not detected")
        status_layout.addWidget(self.port_label, 1, 1)

        # Baud rate
        status_layout.addWidget(QLabel("Baud Rate:"), 2, 0)
        self.baud_label = QLabel(str(self.config.get('baud', 2000000)))
        status_layout.addWidget(self.baud_label, 2, 1)

        # Last message
        status_layout.addWidget(QLabel("Last Message:"), 3, 0)
        self.last_msg_label = QLabel("Never")
        status_layout.addWidget(self.last_msg_label, 3, 1)

        # Message count
        status_layout.addWidget(QLabel("Messages Received:"), 4, 0)
        self.msg_count_label = QLabel("0")
        status_layout.addWidget(self.msg_count_label, 4, 1)

        layout.addWidget(status_frame)

        # Control buttons
        button_layout = QHBoxLayout()

        self.connect_btn = QPushButton("Connect")
        self.connect_btn.setStyleSheet("QPushButton { background-color: #27ae60; color: white; font-weight: bold; padding: 8px; }")
        self.connect_btn.clicked.connect(self.toggle_connection)
        button_layout.addWidget(self.connect_btn)

        self.refresh_btn = QPushButton("Refresh Ports")
        self.refresh_btn.setStyleSheet("QPushButton { background-color: #3498db; color: white; font-weight: bold; padding: 8px; }")
        self.refresh_btn.clicked.connect(self.refresh_ports)
        button_layout.addWidget(self.refresh_btn)

        self.clear_btn = QPushButton("Clear Log")
        self.clear_btn.setStyleSheet("QPushButton { background-color: #f39c12; color: white; font-weight: bold; padding: 8px; }")
        self.clear_btn.clicked.connect(self.clear_log)
        button_layout.addWidget(self.clear_btn)

        layout.addLayout(button_layout)

        # Message log
        log_label = QLabel("Message Log:")
        log_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(log_label)

        self.log_text = QTextEdit()
        self.log_text.setFont(QFont("Courier", 10))
        self.log_text.setStyleSheet("QTextEdit { background-color: #2c3e50; color: #ecf0f1; }")
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)

    def log_message(self, message):
        """Add a message to the log with timestamp"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)

        # Limit log size
        if self.log_text.document().blockCount() > 1000:
            cursor = self.log_text.textCursor()
            cursor.movePosition(cursor.Start)
            cursor.movePosition(cursor.Down, cursor.KeepAnchor, 100)
            cursor.removeSelectedText()

    def clear_log(self):
        """Clear the message log"""
        self.log_text.clear()
        self.message_count = 0
        self.msg_count_label.setText("0")

    def refresh_ports(self):
        """Refresh available serial ports"""
        try:
            port = auto_select_port()
            if port:
                self.current_port = port
                self.port_label.setText(port)
                self.log_message(f"Found ESP32 port: {port}")
            else:
                self.current_port = None
                self.port_label.setText("Not detected")
                self.log_message("No ESP32 port detected")
        except Exception as e:
            self.log_message(f"Error refreshing ports: {e}")

    def toggle_connection(self):
        """Toggle connection to ESP32"""
        if self.is_connected:
            self.disconnect()
        else:
            self.connect()

    def connect(self):
        """Connect to ESP32"""
        if not self.current_port:
            self.refresh_ports()
            if not self.current_port:
                self.log_message("ERROR: No ESP32 port found. Please connect ESP32 and try again.")
                return

        # Create worker thread
        self.worker_thread = QThread()
        self.worker = ConnectionWorker()
        self.worker.moveToThread(self.worker_thread)

        # Connect signals
        self.worker.message_received.connect(self.on_message_received)
        self.worker.connection_status_changed.connect(self.on_connection_status_changed)

        # Start connection
        self.worker_thread.started.connect(
            lambda: self.worker.connect_to_esp32(self.current_port, self.config.get('baud', 2000000))
        )
        self.worker_thread.start()

        self.log_message(f"Connecting to {self.current_port}...")

    def disconnect(self):
        """Disconnect from ESP32"""
        if self.worker:
            self.worker.disconnect_from_esp32()

        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.quit()
            self.worker_thread.wait()

        self.worker = None
        self.worker_thread = None

    def on_message_received(self, message):
        """Handle received message from ESP32"""
        self.message_count += 1
        self.msg_count_label.setText(str(self.message_count))
        self.last_msg_label.setText(datetime.now().strftime("%H:%M:%S"))
        self.log_message(f"RX: {message}")

    def on_connection_status_changed(self, connected, message):
        """Handle connection status change"""
        self.is_connected = connected

        if connected:
            self.status_label.setText("Connected")
            self.status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
            self.connect_btn.setText("Disconnect")
            self.connect_btn.setStyleSheet("QPushButton { background-color: #e74c3c; color: white; font-weight: bold; padding: 8px; }")
        else:
            self.status_label.setText("Disconnected")
            self.status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
            self.connect_btn.setText("Connect")
            self.connect_btn.setStyleSheet("QPushButton { background-color: #27ae60; color: white; font-weight: bold; padding: 8px; }")
            self.last_msg_label.setText("Never")

        self.log_message(message)

    def update_ui(self):
        """Periodic UI updates"""
        pass  # Can add periodic updates here if needed

    def closeEvent(self, event):
        """Handle application closing"""
        self.disconnect()
        event.accept()


def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # Modern look

    window = ESP32ConnectionApp()
    window.show()

    sys.exit(app.exec_())
