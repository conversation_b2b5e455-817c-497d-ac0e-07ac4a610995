import math, time

class DummyPolicy:
    """Replace with your RL policy. Must expose .act(state)->(pwm, mode, flags)"""
    def __init__(self):
        self._k = 0

    def act(self, state:dict):
        # Example: sine sweep between PWM 100..200; brake when vbat low
        self._k += 1
        pwm = int(150 + 50*math.sin(self._k/15.0))
        mode = 0
        flags = 0
        if state and state.get('vbat_mv', 8000) < 6800:
            pwm = 0
            mode = 1  # brake
        return pwm, mode, flags
