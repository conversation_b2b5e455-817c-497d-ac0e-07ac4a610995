def parse_telemetry_line(line:str):
    # Expect gateway CSV beginning with 'T,' e.g. T,12,0,0,1000,0,0,0,7420
    if not line.startswith('T,'):
        return None
    parts = line.split(',')
    if len(parts) < 9:
        return None
    try:
        return {
            'seq': int(parts[1]),
            'ax': int(parts[2]),
            'ay': int(parts[3]),
            'az': int(parts[4]),
            'gx': int(parts[5]),
            'gy': int(parts[6]),
            'gz': int(parts[7]),
            'vbat_mv': int(parts[8]),
        }
    except Exception:
        return None
