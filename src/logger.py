import os, csv, time

class CsvLogger:
    def __init__(self, path:str):
        self.path = path
        os.makedirs(os.path.dirname(path), exist_ok=True)
        self._fh = open(path, 'w', newline='')
        self._wr = csv.writer(self._fh)
        self._wr.writerow(['ts', 'seq', 'ax','ay','az','gx','gy','gz','vbat_mv'])

    def write(self, row:dict):
        ts = time.time()
        self._wr.writerow([ts, row.get('seq'), row.get('ax'), row.get('ay'), row.get('az'),
                           row.get('gx'), row.get('gy'), row.get('gz'), row.get('vbat_mv')])
        self._fh.flush()

    def close(self):
        try:
            self._fh.close()
        except Exception:
            pass
