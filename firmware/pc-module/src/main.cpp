#include <Arduino.h>
#include <esp_system.h>

// ===== ESP32 PC Module Firmware =====
// Compatible with Python serial_link.py
// Receives commands: C,pwm,mode,flags
// Sends telemetry: T,seq,status,vbat,temp,uptime

#ifndef LED_PIN
#define LED_PIN 2
#endif
#ifndef LED_ACTIVE_LOW
#define LED_ACTIVE_LOW 0 // set to 1 if your LED turns on with LOW
#endif

// State variables
static bool led_on = false;
static unsigned long seq_counter = 0;
static unsigned long last_telemetry_ms = 0;
static unsigned long last_command_ms = 0;
static unsigned long boot_time_ms = 0;

// Command state
static int current_pwm = 0;
static int current_mode = 0;
static int current_flags = 0;

// Telemetry interval (ms)
static const unsigned long TELEMETRY_INTERVAL = 100; // 10 Hz

static inline void ledWrite(bool on) {
#if LED_ACTIVE_LOW
  digitalWrite(LED_PIN, on ? LOW : HIGH);
#else
  digitalWrite(LED_PIN, on ? HIGH : LOW);
#endif
}

static float getVoltage() {
  // Simulate battery voltage (you can replace with actual ADC reading)
  return 3.3 + (random(-100, 100) / 1000.0); // 3.2-3.4V range
}

static float getTemperature() {
  // Simulate temperature reading
  return 25.0 + (random(-50, 50) / 10.0); // 20-30°C range
}

static void sendTelemetry() {
  unsigned long uptime_ms = millis() - boot_time_ms;
  float vbat = getVoltage();
  float temp = getTemperature();

  // Format: T,seq,status,vbat,temp,uptime
  Serial.printf("T,%lu,%d,%.2f,%.1f,%lu\n",
                seq_counter++,
                (led_on ? 1 : 0),
                vbat,
                temp,
                uptime_ms);
}

static void processCommand(String cmd) {
  // Expected format: C,pwm,mode,flags
  if (!cmd.startsWith("C,")) {
    return;
  }

  // Parse command
  int comma1 = cmd.indexOf(',', 2);
  int comma2 = cmd.indexOf(',', comma1 + 1);
  int comma3 = cmd.indexOf(',', comma2 + 1);

  if (comma1 == -1 || comma2 == -1 || comma3 == -1) {
    Serial.println("ERROR: Invalid command format");
    return;
  }

  current_pwm = cmd.substring(2, comma1).toInt();
  current_mode = cmd.substring(comma1 + 1, comma2).toInt();
  current_flags = cmd.substring(comma2 + 1, comma3).toInt();

  last_command_ms = millis();

  // Update LED based on PWM value (simple indication)
  led_on = (current_pwm > 0);
  ledWrite(led_on);

  // Send acknowledgment
  Serial.printf("ACK: PWM=%d, MODE=%d, FLAGS=%d\n", current_pwm, current_mode, current_flags);
}

void setup() {
  Serial.begin(2000000); // Match Python config baud rate
  delay(1000); // let CH340/host settle

  pinMode(LED_PIN, OUTPUT);
  ledWrite(led_on);

  boot_time_ms = millis();
  randomSeed(analogRead(0)); // Seed for simulated sensor data

  Serial.println();
  Serial.println(F("=== ESP32 PC Module Ready ==="));
  Serial.printf("SDK: %s\n", ESP.getSdkVersion());
  Serial.printf("Chip: %s | Cores: %d | Rev: %d\n",
                ESP.getChipModel(), ESP.getChipCores(), ESP.getChipRevision());
  Serial.printf("CPU: %d MHz | Free Heap: %u bytes\n",
                ESP.getCpuFreqMHz(), ESP.getFreeHeap());
  Serial.println(F("Protocol: C,pwm,mode,flags -> T,seq,status,vbat,temp,uptime"));
  Serial.println(F("Ready for commands..."));

  last_telemetry_ms = millis();
}

void loop() {
  unsigned long now = millis();

  // Handle incoming serial commands
  if (Serial.available() > 0) {
    String command = Serial.readStringUntil('\n');
    command.trim();

    if (command.length() > 0) {
      processCommand(command);
    }
  }

  // Send periodic telemetry
  if (now - last_telemetry_ms >= TELEMETRY_INTERVAL) {
    sendTelemetry();
    last_telemetry_ms = now;
  }

  // Connection timeout check (if no commands received for 5 seconds, turn off LED)
  if (last_command_ms > 0 && (now - last_command_ms > 5000)) {
    if (led_on) {
      led_on = false;
      ledWrite(led_on);
      Serial.println("TIMEOUT: No commands received, LED off");
    }
  }

  // Small delay to prevent tight loop
  delay(1);
}