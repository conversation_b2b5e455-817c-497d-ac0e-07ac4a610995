#include <Arduino.h>
#include <esp_system.h>

// ===== Simple ESP32 self-test (Serial + Blink) =====
// Works with CH340 USB-serial. Open Serial Monitor at 115200.
// You can override these via platformio.ini build_flags, e.g.:
// build_flags = -DLED_PIN=2 -DLED_ACTIVE_LOW=0
#ifndef LED_PIN
#define LED_PIN 2
#endif
#ifndef LED_ACTIVE_LOW
#define LED_ACTIVE_LOW 0 // set to 1 if your LED turns on with LOW
#endif

static bool led_on = true;       // logical ON state
static bool blink_enabled = true;
static unsigned long blink_ms = 500;
static unsigned long last_ms = 0;

static inline void ledWrite(bool on) {
#if LED_ACTIVE_LOW
  digitalWrite(LED_PIN, on ? LOW : HIGH);
#else
  digitalWrite(LED_PIN, on ? HIGH : LOW);
#endif
}

static const char* resetReasonToStr(esp_reset_reason_t r) {
  switch (r) {
    case ESP_RST_UNKNOWN:   return "UNKNOWN";
    case ESP_RST_POWERON:   return "POWERON";
    case ESP_RST_EXT:       return "EXTERNAL";
    case ESP_RST_SW:        return "SW";
    case ESP_RST_PANIC:     return "PANIC";
    case ESP_RST_INT_WDT:   return "INT_WDT";
    case ESP_RST_TASK_WDT:  return "TASK_WDT";
    case ESP_RST_WDT:       return "WDT";
    case ESP_RST_DEEPSLEEP: return "DEEPSLEEP";
    case ESP_RST_BROWNOUT:  return "BROWNOUT";
    case ESP_RST_SDIO:      return "SDIO";
    default:                return "(other)";
  }
}

static void printInfo() {
  Serial.println();
  Serial.println(F("=== ESP32 Self-Test ==="));
  Serial.printf("SDK: %s\n", ESP.getSdkVersion());
  Serial.printf("Chip: %s | cores: %d | rev: %d\n", ESP.getChipModel(), ESP.getChipCores(), ESP.getChipRevision());
  Serial.printf("CPU: %d MHz | Flash: %u bytes\n", ESP.getCpuFreqMHz(), ESP.getFlashChipSize());
  Serial.printf("Heap free: %u bytes\n", ESP.getFreeHeap());
  Serial.printf("Reset reason: %s\n", resetReasonToStr(esp_reset_reason()));
  Serial.printf("LED pin: %d (active %s) | Blink: %s @ %lu ms\n",
                LED_PIN, LED_ACTIVE_LOW ? "LOW" : "HIGH",
                blink_enabled ? "ENABLED" : "DISABLED", blink_ms);
  Serial.println(F("Commands: i=info, l=toggle LED, b/B=faster/slower, s=stop, o=start, r=restart"));
}

void setup() {
  Serial.begin(115200);
  delay(800); // let CH340/host settle

  pinMode(LED_PIN, OUTPUT);
  ledWrite(led_on);

  Serial.println();
  Serial.println(F("Booting: ESP32 Test (Serial + Blink)"));
  Serial.println(F("setup() reached"));
  printInfo();
}

void loop() {
  // Serial command handler
  while (Serial.available() > 0) {
    char c = (char)Serial.read();
    switch (c) {
      case 'i': case 'I':
        printInfo();
        break;
      case 'l': case 'L':
        led_on = !led_on;
        ledWrite(led_on);
        Serial.printf("LED %s\n", led_on ? "ON" : "OFF");
        break;
      case 'b': // faster
        if (blink_ms > 50) blink_ms /= 2;
        blink_enabled = true;
        Serial.printf("Blink @ %lu ms\n", blink_ms);
        break;
      case 'B': // slower
        if (blink_ms < 2000) blink_ms *= 2;
        blink_enabled = true;
        Serial.printf("Blink @ %lu ms\n", blink_ms);
        break;
      case 's': case 'S':
        blink_enabled = false;
        Serial.println("Blink DISABLED");
        break;
      case 'o': case 'O':
        blink_enabled = true;
        Serial.printf("Blink ENABLED @ %lu ms\n", blink_ms);
        break;
      case 'r': case 'R':
        Serial.println("Restarting in 500 ms...");
        delay(500);
        ESP.restart();
        break;
      default:
        Serial.printf("[echo] 0x%02X '%c'\n", (unsigned char)c, (c >= 32 && c < 127) ? c : '.');
        break;
    }
  }

  // Blink logic
  unsigned long now = millis();
  if (blink_enabled && (now - last_ms >= blink_ms)) {
    last_ms = now;
    led_on = !led_on;
    ledWrite(led_on);
  }
}