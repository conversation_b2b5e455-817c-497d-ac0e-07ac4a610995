#!/bin/bash

# <PERSON>SP32 PC Module Firmware Upload Script
# Make sure you have PlatformIO installed: pip install platformio

echo "=== ESP32 PC Module Firmware Upload ==="
echo "Make sure your ESP32 is connected via USB"
echo ""

# Check if platformio is available
if ! command -v pio &> /dev/null; then
    echo "ERROR: PlatformIO not found!"
    echo "Install with: pip install platformio"
    exit 1
fi

# Build and upload
echo "Building and uploading firmware..."
pio run --target upload

if [ $? -eq 0 ]; then
    echo ""
    echo "✓ Upload successful!"
    echo "✓ ESP32 PC Module firmware is ready"
    echo ""
    echo "Next steps:"
    echo "1. Open Serial Monitor: pio device monitor"
    echo "2. Or run Python app: python -m src.main"
    echo ""
else
    echo ""
    echo "✗ Upload failed!"
    echo "Check your ESP32 connection and try again"
    exit 1
fi
