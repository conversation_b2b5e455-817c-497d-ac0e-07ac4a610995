# ESP32 PC Module Connection Monitor

A streamlined Python application for monitoring and communicating with ESP32 firmware for the PC module.

## Features

- **Auto-detection**: Automatically finds ESP32 serial ports
- **Real-time monitoring**: Shows connection status and incoming messages
- **Interactive console**: Simple command-line interface
- **Keep-alive**: Sends periodic commands to maintain connection
- **Clean architecture**: Minimal dependencies, focused functionality

## Quick Start

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Connect your ESP32** device via USB

3. **Run the monitor**:
   ```bash
   python -m src.main
   ```

## Usage

The application provides an interactive console with these commands:

- `c` - Connect to ESP32
- `d` - Disconnect from ESP32
- `s` - Show connection status
- `r` - Refresh available ports
- `q` - Quit application

## Project Structure

- `src/main.py`: Main application with console interface
- `src/serial_link.py`: Serial communication handling
- `src/serial_utils.py`: Port detection and utilities
- `src/config.json`: Configuration (port, baud rate)
- `firmware/`: ESP32 Arduino sketches (if available)

## Configuration

Edit `src/config.json` to configure:
- `port`: Serial port (use "auto" for automatic detection)
- `baud`: Baud rate (default: 2000000)

## Requirements

- Python 3.7+
- pyserial
- ESP32 device with compatible firmware

## Permissions
- **Linux**: add your user to the `dialout` group, then re-login: `sudo usermod -a -G dialout $USER`
- **macOS**: you may need to allow the USB device in *Security & Privacy* after first connect.
- **Windows**: port will be like `COM7`. If it doesn't appear, (re)install CP210x/CH34x drivers depending on your board.
