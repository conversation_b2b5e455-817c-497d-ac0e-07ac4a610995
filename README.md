# RL Car – PC Link (PyCharm starter)

This is a minimal, production-friendly starter to read telemetry from your ESP32 **gateway** over USB,
log it to CSV, and send back commands (PWM, mode, flags).
It matches the Arduino sketches we set up (gateway prints `T,seq,ax,ay,az,gx,gy,gz,vbat`).

## Quick start (PyCharm)

1. **Open** this folder in PyCharm (`File → Open…`).
2. When prompted, create a **new virtual environment** (venv).
3. PyCharm will detect `requirements.txt` → click *Install requirements*.
4. Connect your **gateway ESP32-S3** via USB.
5. Find the serial port name (Windows: `COMx`, Linux: `/dev/ttyACM0` or `/dev/ttyUSB0`, macOS: `/dev/tty.usbmodem...`).
6. Edit `src/config.json` and set `"port"` to your device, adjust baud if needed.
7. Run **`src/main.py`** (green triangle). You should see telemetry rows printed & saved to `logs/telemetry.csv`.

## Permissions
- **Linux**: add your user to the `dialout` group, then re-login: `sudo usermod -a -G dialout $USER`
- **macOS**: you may need to allow the USB device in *Security & Privacy* after first connect.
- **Windows**: port will be like `COM7`. If it doesn't appear, (re)install CP210x/CH34x drivers depending on your board.

## CSV Format
Each telemetry line is parsed into:
- `seq, ax, ay, az, gx, gy, gz, vbat_mv`

## Sending Commands
From the terminal panel in PyCharm you can run:
```
python -m src.cli --pwm 150 --mode 0 --flags 0
```
or use the **Live keys** inside `main.py` (W/S to increase/decrease PWM; B toggles brake).

## Next steps
- Replace the dummy policy in `policy.py` with your RL inference (PyTorch/ONNX, etc.).
- Switch to a **binary frame** over serial if you want more throughput/less parsing.
- Add extra fields (INA260 current, ToF distances) in both firmware and `serial_link.py` parser.
